
# Structure d’Interface – **SIGMA**
*Version : 1.0 – mis à jour le 09 July 2025*  
*Auteur : Aymeric*

> **Objectif**  
> Décrire une interface utilisateur **PWA** moderne, réactive et **Firebase‑native**, prête à être implémentée (ou générée) par un agent IA.  
> Chaque section précise : **but fonctionnel**, **composants/écrans**, **flux d’interaction**, hooks Firebase et KPI UX.

---

## 1 · Dashboard « Résumé »

| Zone | Contenu temps réel | Hooks Firebase |
|------|--------------------|----------------|
| **Alertes** | Stocks bas, matériel manquant, modules en maintenance | `onSnapshot` collection `stocks` + Cloud Messaging pour #critique |
| **Emprunts prioritaires** | *Overdue*, départs H‑24, attente facture | Query composite `status+dateRetour` |
| **Actions rapides** | ➕ Nouvel emprunt • 🔄 Réassort • 🛠 Modules KO • 💰 Facturation | Buttons ➜ Callable CF |

> **UX KPI** : 1 s TTFB, 100 ms refresh RT, accessibilité offline (cache Firestore).

---

## 2 · Emprunts

### 2.1 Liste

- **Table virtualisée** (1000+ lignes sans jank).  
- Filtres instantanés (statut, date, secteur).  
- Colonnes : ID • Manip • Lieu • D Départ • D Retour • Statut / Couleur.  
- **Pagination adaptative** : `startAfter` + 50 docs.

### 2.2 Détail

| Bloc | Fonction | Firebase |
|------|----------|----------|
| En‑tête | Infos générales (RT) | Listener doc (`emprunts/<built-in function id>`) |
| Contenu | Modules & matériel liés | Sub‑collection `items` |
| Historique | Traçabilité (audit) | Sub‑collection `logs` |
| Actions | *Départ* • *Retour* • PDF | Callable CF `emprunts/checkout` |

### 2.3 Wizard Création

1. **Base** (nom, lieu, dates)  
2. **Sélection matériel** (suggestions AI, disponibilité RT)  
3. **Livraison (optionnel)**  
4. **Validation** ➜ Transaction CF  
> **Autosave** brouillon `status=draft`.

---

## 3 · Modules

| Écran | Key features |
|-------|--------------|
| **Liste** | Filtre statut, stats usage (`functions.aggregateUsage`) |
| **Fiche** | RT contenu, démantèlement sécurisé, dépendances (emprunts actifs) |
| **Démanteler / Dupliquer** | Batch transaction • update index |

---

## 4 · Stocks

### 4.1 Tableau stock

- **Graphique seuils** (Matplotlib JS)  
- Prévision conso (`functions.predictUsage`)  
- Filtres croisés (catégorie, fournisseur).

### 4.2 Fiche article

| Champ | Source |
|-------|--------|
| Réf fournisseur, prix | `stocks/<built-in function id>` |
| Historique mouvements | Sub‑collection `mouvements` |
| **QR Code** | Généré CF + Storage |

---

## 5 · Livraisons

- **Carte Leaflet** (livraisons H‑7).  
- Statut RT (`livraisons/<built-in function id>`) + notifications.  
- Étape optim. itinéraire (TSP API optionnel).

---

## 6 · Paramètres & Admin

| Module | Fonction |
|--------|----------|
| **Utilisateurs** | Rôles via Custom Claims |
| **Fournisseurs** | CRUD + recherche |
| **Seuils alertes** | Champ `settings/thresholds` |
| **Mode maintenance** | Toggle `config/maintenance` |

---

## 7 · Architecture UI (dossiers)

```text
src/
├── html/          # Templates GAS
│   ├── dashboard.html
│   ├── emprunts.html
│   └── …
├── js/
│   ├── main.js
│   ├── firebaseUtils.js
│   ├── components/     # Web components
│   │   ├── data-table.js
│   │   └── …
│   ├── views/
│   │   ├── dashboardView.js
│   │   ├── empruntsView.js
│   │   └── …
│   └── domain/        # Calls to CF / Firestore
│       ├── empruntsData.js
│       └── …
└── css/
    └── styles.css
```

---

## 8 · Navigation & Recherche

- **Sidebar** hiérarchique + badges alertes.  
- **Breadcrumb** & onglets récents.  
- **Recherche globale** ➜ end‑point CF `searchGlobal` (Algolia optionnel).  

---

## 9 · UX + PWA

| Feature | Implémentation |
|---------|----------------|
| Mode sombre | CSS prefers‑color‑scheme |
| Offline | Firestore cache + `workbox` |
| Notifications | FCM + local dev token |
| Shortcuts clavier | `cmd+k` search, `n` nouvel emprunt |

---

## 10 · Sécurité & Performance

- Règles Firestore fines (`role`‑based).  
- Transactions CF pour toute mutation critique.  
- **Audit trail** sub‑collection `logs`.  
- Lighthouse PWA score > 90.

---

*Fin*  
