#!/usr/bin/env bash
#
# sigma_task_runner.sh (v2 - Orchestrateur d'Agent I<PERSON>)
# ------------------------------------------------------------
# Utilise un framework de prompts pour exécuter une tâche SIGMA.
# ------------------------------------------------------------
set -euo pipefail

# --- Configuration ---
PROMPT_TEMPLATES_FILE="prompt_templates/templates.yaml"

# --- Validation des arguments et dépendances ---
if [[ $# -lt 1 ]]; then
  echo "Usage: $0 TASK_ID [tasks_file]" >&2
  exit 1
fi
TASK_ID="$1"
TASKS_FILE="${2:-SIGMA_tasks.yaml}"

# ... (gardez vos vérifications pour yq, jq, et l'existence du fichier de tâches) ...

# --- Extraction et affichage de la tâche ---
task_json=$(yq -o=json '.[] | select(.task_id=="'"$TASK_ID"'")' "$TASKS_FILE" || true)
if [[ -z "$task_json" ]]; then
  echo "Tâche $TASK_ID non trouvée dans $TASKS_FILE" >&2
  exit 1
fi

kind=$(echo "$task_json" | jq -r '.kind')
desc=$(echo "$task_json" | jq -r '.description')
status=$(echo "$task_json" | jq -r '.status')

echo "------------------------------------------------------------"
echo "Tâche       : $TASK_ID"
echo "Description : $desc"
# ... (affichez les autres détails) ...
echo "------------------------------------------------------------"

if [[ "$status" == "done" ]]; then
  echo "La tâche est déjà marquée comme terminée. Rien à faire."
  exit 0
fi

# --- Préparation de la branche Git ---
branch="auto/${TASK_ID,,}"
# ... (gardez votre logique de création de branche) ...

# ------------------------------------------------------------------
# 1. Génération du prompt à partir des modèles
# ------------------------------------------------------------------
echo "Génération du prompt pour la tâche de type '$kind'..."

# Sélection du template system/user basé sur le 'kind' de la tâche
SYSTEM_PROMPT_TEMPLATE=$(yq ".$kind.system" "$PROMPT_TEMPLATES_FILE")
USER_PROMPT_TEMPLATE=$(yq ".$kind.user" "$PROMPT_TEMPLATES_FILE")

if [[ "$SYSTEM_PROMPT_TEMPLATE" == "null" || "$USER_PROMPT_TEMPLATE" == "null" ]]; then
  echo "Erreur : Aucun modèle de prompt trouvé pour le type '$kind' dans $PROMPT_TEMPLATES_FILE" >&2
  exit 1
fi

# Fonction pour remplacer les placeholders
# Note : une approche plus robuste utiliserait un outil comme 'envsubst' ou un script Python/Node.
instantiate_prompt() {
    local template="$1"
    local json_data="$2"
    
    # Extraire les valeurs
    local task_id=$(echo "$json_data" | jq -r '.task_id')
    local description=$(echo "$json_data" | jq -r '.description')
    # Ajoutez ici l'extraction pour les autres placeholders...
    # Ex: local files=$(echo "$json_data" | jq -r '.target_files | @json')

    # Remplacer les placeholders
    # C'est une simple substitution, à adapter selon la complexité
    template="${template//\{\{TASK_ID\}\}/$task_id}"
    template="${template//\{\{DESCRIPTION\}\}/$description}"
    # ... et ainsi de suite pour les autres ...
    
    echo "$template"
}

USER_PROMPT_FINAL=$(instantiate_prompt "$USER_PROMPT_TEMPLATE" "$task_json")

# ------------------------------------------------------------------
# 2. Invocation de l'Agent Augment IA
# ------------------------------------------------------------------
echo "Invocation de l'agent Augment..."
# Note: La syntaxe exacte dépend de votre CLI. Ceci est un exemple plausible.
# Le prompt est passé via stdin pour éviter les problèmes avec les caractères spéciaux.
echo "$USER_PROMPT_FINAL" | augment agent --system-prompt "$SYSTEM_PROMPT_TEMPLATE"

# Optionnel mais recommandé : implémenter la boucle de correction ici
# MAX_RETRIES=3
# for i in $(seq 1 $MAX_RETRIES); do
#   # ... appel à l'agent ...
#   # ... exécution des tests ...
#   if [ $? -eq 0 ]; then
#     echo "✅ Tests passés au tour $i."
#     break # Sortie de la boucle
#   else
#     # ... capturer le log d'erreur et le renvoyer à l'agent ...
#   fi
# done

# ------------------------------------------------------------------
# 3. Exécution des tests et commit
# ------------------------------------------------------------------
# ... (gardez votre logique de tests et de commit/push) ...

echo "------------------------------------------------------------"
echo "Processus terminé pour $TASK_ID. Créez une Pull Request."
echo "------------------------------------------------------------"