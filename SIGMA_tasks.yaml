- task_id: ATELIER_ROLES_PERMISSIONS
  sprint: 0
  priority: critical
  kind: planning
  description: Atelier rôles/permissions
  status: todo
- task_id: GEL_DU_MODELE_DE_DONNEES_V0_9
  sprint: 0
  priority: critical
  kind: documentation
  description: Gel du modèle de données v0.9
  status: todo
- task_id: STORYMAPPING_UI
  sprint: 0
  priority: opportunity
  kind: planning
  description: Story‑mapping UI
  status: todo
- task_id: ECRIRE_FIRESTORE_RULES
  sprint: 1
  priority: critical
  kind: firestore_rules
  description: <PERSON><PERSON><PERSON><PERSON> firestore.rules
  status: todo
- task_id: TESTS_SIMULATEUR
  sprint: 1
  priority: critical
  kind: tests
  description: Tests simulateur
  status: todo
- task_id: ACTIVER_AUTH_GOOGLE
  sprint: 1
  priority: important
  kind: configuration
  description: Activer Auth (Google)
  status: done
- task_id: CF_SETUSERROLE_CALLABLE
  sprint: 1
  priority: critical
  kind: cloud_function
  description: CF setUserRole (Callable)
  status: todo
- task_id: CF_ASSIGNDEFAULTROLEONCREATEUSER_TRIGGER
  sprint: 1
  priority: critical
  kind: cloud_function
  description: CF assignDefaultRoleOnCreateUser (Trigger)
  status: todo
- task_id: PIPELINE_CI_CD_LINTTESTSDEPLOY_DEV
  sprint: 1
  priority: critical
  kind: ci_cd
  description: Pipeline CI/CD lint‑tests‑deploy dev
  status: todo
- task_id: CONFIGURER_SECRETS_GITHUB
  sprint: 1
  priority: opportunity
  kind: ci_cd
  description: Configurer Secrets GitHub
  status: todo
- task_id: UI_ADMIN_USERS_HTML_USERS_JS
  sprint: 1
  priority: critical
  kind: ui
  description: UI admin/users.html + users.js
  status: todo
- task_id: CF_CREATEEMPRUNTCF_CALLABLE
  sprint: 3
  priority: critical
  kind: cloud_function
  description: CF createEmpruntCF (Callable)
  status: todo
- task_id: CF_TRIGGEREMPRUNTDEPARTCF_CALLABLE
  sprint: 3
  priority: critical
  kind: cloud_function
  description: CF triggerEmpruntDepartCF (Callable)
  status: todo
- task_id: CF_TRIGGEREMPRUNTRETOURCF_CALLABLE
  sprint: 3
  priority: critical
  kind: cloud_function
  description: CF triggerEmpruntRetourCF (Callable)
  status: todo
- task_id: INDEX_COMPOSITES_FIRESTORE_EMPRUNTS
  sprint: 3
  priority: important
  kind: firestore
  description: Index composites Firestore emprunts
  status: todo
- task_id: LISTENER_ALERTES_STOCK
  sprint: 3
  priority: critical
  kind: ui
  description: Listener Alertes stock
  status: todo
- task_id: LISTENER_EMPRUNTS_RETARD
  sprint: 3
  priority: critical
  kind: ui
  description: Listener Emprunts retard
  status: todo
- task_id: OPTIMISER_REQUETES_DASHBOARD
  sprint: 3
  priority: opportunity
  kind: optimization
  description: Optimiser requêtes Dashboard
  status: todo
- task_id: UI_FORMULAIRE_CREATION_MULTIETAPES
  sprint: 5
  priority: critical
  kind: ui
  description: UI Formulaire création multi‑étapes
  status: todo
- task_id: PDF_CONFIRMATION_GENERATELABELPDFCF
  sprint: 5
  priority: critical
  kind: cloud_function
  description: PDF confirmation generateLabelPDFCF
  status: todo
- task_id: TESTS_CYPRESS_FLUX_EMPRUNT_COMPLET
  sprint: 5
  priority: important
  kind: tests
  description: Tests Cypress flux emprunt complet
  status: todo
- task_id: CF_SAVEMODULECF_TRANSACTION
  sprint: 7
  priority: critical
  kind: cloud_function
  description: CF saveModuleCF (transaction)
  status: todo
- task_id: CF_DISMANTLEMODULECF
  sprint: 7
  priority: critical
  kind: cloud_function
  description: CF dismantleModuleCF
  status: todo
- task_id: TRIGGER_CHECKMODULEREADINESSONUPDATECF
  sprint: 7
  priority: important
  kind: cloud_function
  description: Trigger checkModuleReadinessOnUpdateCF
  status: todo
- task_id: COUVERTURE_TESTS_CF_70
  sprint: 7
  priority: important
  kind: tests
  description: Couverture tests CF ≥ 70%
  status: todo
- task_id: CF_RECORDSTOCKMOVEMENTCF_TRANSACTION
  sprint: 9
  priority: critical
  kind: cloud_function
  description: CF recordStockMovementCF (transaction)
  status: todo
- task_id: ALERTES_SEUIL_STOCK_IN_TRANSACTION
  sprint: 9
  priority: critical
  kind: cloud_function
  description: Alertes seuil stock in-transaction
  status: todo
- task_id: UI_LISTE_FILTRES_STOCKS
  sprint: 9
  priority: critical
  kind: ui
  description: UI Liste & filtres Stocks
  status: todo
- task_id: CF_SCHEDULED_BACKUPFIRESTORETOGCS
  sprint: 9
  priority: critical
  kind: cloud_function
  description: CF Scheduled backupFirestoreToGCS
  status: todo
- task_id: SCRIPT_RESTAURATION_TEST_MENSUELLE
  sprint: 9
  priority: important
  kind: scripts
  description: Script restauration test mensuelle
  status: todo
- task_id: CF_PLANDELIVERYCF
  sprint: 11
  priority: critical
  kind: cloud_function
  description: CF planDeliveryCF
  status: todo
- task_id: UI_CARTE_LEAFLET_LIVRAISONS
  sprint: 11
  priority: critical
  kind: ui
  description: UI Carte Leaflet livraisons
  status: todo
- task_id: UPLOAD_PREUVES_STORAGE_REGLES
  sprint: 11
  priority: important
  kind: storage
  description: Upload preuves Storage + règles
  status: todo
- task_id: DASHBOARD_CLOUD_MONITORING
  sprint: 11
  priority: critical
  kind: monitoring
  description: Dashboard Cloud Monitoring
  status: todo
- task_id: ALERTES_LATENCE_500_MS
  sprint: 11
  priority: critical
  kind: monitoring
  description: Alertes latence >500 ms
  status: todo
- task_id: AUDIT_OWASP_PENTEST
  sprint: 13
  priority: critical
  kind: security
  description: Audit OWASP + pentest
  status: todo
- task_id: TESTS_CHARGE_ARTILLERY
  sprint: 13
  priority: critical
  kind: tests
  description: Tests charge Artillery
  status: todo
- task_id: ACCESSIBILITE_WCAG_AA
  sprint: 13
  priority: important
  kind: accessibility
  description: Accessibilité WCAG AA
  status: todo
- task_id: PILOTE_5_UTILISATEURS
  sprint: 13
  priority: important
  kind: pilot
  description: Pilote 5 utilisateurs
  status: todo
- task_id: CONGELATION_CODE_TAG_V1_0_0
  sprint: 15
  priority: critical
  kind: release
  description: Congélation code tag v1.0.0
  status: todo
- task_id: CHECKLIST_PREPROD_ROLLBACK_TESTE
  sprint: 15
  priority: critical
  kind: release
  description: Checklist pré‑prod + rollback testé
  status: todo
- task_id: SMOKE_TESTS_PROD
  sprint: 15
  priority: important
  kind: tests
  description: Smoke tests prod
  status: todo
- task_id: COLLECTE_FEEDBACK_UTILISATEURS
  sprint: 16
  priority: critical
  kind: feedback
  description: Collecte feedback utilisateurs
  status: todo
- task_id: PLANIFICATION_ROADMAP_V1_1
  sprint: 16
  priority: critical
  kind: planning
  description: Planification Roadmap v1.1
  status: todo
- task_id: AMELIORATION_ONBOARDING_DOCS
  sprint: 16
  priority: opportunity
  kind: documentation
  description: Amélioration onboarding & docs
  status: todo
