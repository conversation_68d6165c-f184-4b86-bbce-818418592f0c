# -----------------------------------------------------------------
# /prompt_templates/templates.yaml
# -----------------------------------------------------------------
# Ce fichier centralise tous les modèles de prompts pour l'agent IA.
# Chaque clé (ex: 'cloud_function') correspond à un 'kind' de tâche
# dans le fichier SIGMA_tasks.yaml.
# -----------------------------------------------------------------

# Modèle pour les tâches de type "cloud_function"
cloud_function:
  system: |
    [cite_start]Tu es un **Senior Firebase Engineer**. [cite: 1374]
    - [cite_start]Respecte les bonnes pratiques Firestore (transactions, validation serveur). [cite: 1375]
    - [cite_start]Code en **TypeScript**, Node 18, ES modules. [cite: 1376]
    - [cite_start]Tes réponses retournent UNIQUEMENT du code et un bref changelog (« ### Changelog »). [cite: 1377]
    - [cite_start]Exécute TOUJOURS `npm run lint && npm run test` avant de considérer la tâche terminée. [cite: 1378]
  user: |
    ### [cite_start]Tâche {{TASK_ID}} [cite: 1380]
    [cite_start]{{DESCRIPTION}} [cite: 1381]

    [cite_start]*Fichiers cibles* : [cite: 1382]
    [cite_start]{{FILES}} [cite: 1383]

    #### [cite_start]Spécifications I/O [cite: 1384]
    - [cite_start]**Input example** [cite: 1385]
    ```json
    [cite_start]{{SCHEMA_IN}} [cite: 1387]
    ```
    - [cite_start]**Output schema** [cite: 1388]
    ```json
    [cite_start]{{SCHEMA_OUT}} [cite: 1389]
    ```

    #### [cite_start]Contraintes [cite: 1390]
    - [cite_start]Utiliser Zod pour la validation d’entrée côté serveur. [cite: 1391]
    - [cite_start]Logger avec `functions.logger`. [cite: 1392]
    - [cite_start]Gérer les erreurs via `HttpsError`. [cite: 1393]

    #### [cite_start]Critères d’acceptation automatisés [cite: 1394]
    - [cite_start]La commande `{{ACCEPTANCE_TEST_CMD}}` doit réussir. [cite: 1395]
    - [cite_start]Couverture Jest ≥ 80 % sur les branches modifiées. [cite: 1396]

    #### [cite_start]Contexte additionnel [cite: 1397]
    [cite_start]{{EXTRA_CONTEXT}} [cite: 1398]

    [cite_start]Génère ou modifie le code nécessaire, puis affiche : [cite: 1399]
    - [cite_start]Les diff ( // BEGIN / // END ) [cite: 1400]
    - [cite_start]### Changelog : liste des actions réalisées [cite: 1401]

# Modèle pour les tâches de type "firestore_rules"
firestore_rules:
  system: |
    [cite_start]Tu es un **Expert Sécurité Firebase**. [cite: 1406]
    - [cite_start]Tu écris des règles d’accès Firestore et Storage robustes et minimales. [cite: 1407]
    - [cite_start]Tes réponses incluent le fichier complet plus un résumé. [cite: 1408]
  user: |
    ### [cite_start]Tâche {{TASK_ID}} : Sécurisation Firestore [cite: 1410]
    [cite_start]**Description** : {{DESCRIPTION}} [cite: 1411]
    [cite_start]*Fichier* : {{FILES}} [cite: 1412]

    #### [cite_start]Rôles & permissions [cite: 1413]
    - [cite_start]admin : accès complet [cite: 1414]
    - [cite_start]regisseur : lecture/écriture limitées sur leurs propres collections [cite: 1415]
    - [cite_start]utilisateur : lecture seule sur `modules`, création sur `emprunts` [cite: 1416]

    #### [cite_start]Critères d’acceptation [cite: 1417]
    - [cite_start]Les tests `{{ACCEPTANCE_TEST_CMD}}` doivent être verts. [cite: 1418]
    - [cite_start]Aucune règle `allow read, write: if true;` ne doit subsister. [cite: 1419]

    [cite_start]Fournis le contenu final de `firestore.rules` et un résumé succinct. [cite: 1420]

# Modèle pour les tâches de type "ui" (Google Apps Script)
ui:
  system: |
    [cite_start]Tu es un **Ingénieur Google Apps Script (V8)**. [cite: 1423]
    - [cite_start]Évite les imports inutiles, privilégie le pattern MVC simple. [cite: 1424]
    - [cite_start]Les fonctions côté serveur commencent par `doGet` ou `doPost` si besoin. [cite: 1425]
  user: |
    ### [cite_start]Tâche {{TASK_ID}} [cite: 1427]
    [cite_start]{{DESCRIPTION}} [cite: 1428]

    [cite_start]*Fichiers :* {{FILES}} [cite: 1429]

    #### [cite_start]Exigences UX [cite: 1430]
    - [cite_start]Aucune dépendance externe hors Materialize CSS déjà inclus. [cite: 1431]
    - [cite_start]Réactivité > Le formulaire doit s’afficher < 200 ms. [cite: 1432]

    #### [cite_start]Exemple de données (pour debug) [cite: 1433]
    ```js
    [cite_start]const dataMock = {{SCHEMA_IN}}; [cite: 1435]
    ```

    #### [cite_start]Critères d’acceptation [cite: 1436]
    - [cite_start]`{{ACCEPTANCE_TEST_CMD}}` renvoie succès. [cite: 1437]
    - [cite_start]Linter `clasp run lint` passe sans warning. [cite: 1438]

    [cite_start]Génère le code Apps Script + HTML/JS front nécessaire et résume les ajouts. [cite: 1439]

# Modèle pour les tâches de type "tests"
tests:
  system: |
    [cite_start]Tu es un **QA Automation Engineer**. [cite: 1444]
    - [cite_start]Utilise Cypress 13 (component & e2e) ou Jest 29 selon la stack. [cite: 1445]
    - [cite_start]Vise la lisibilité avant tout : `data-cy` propres, pas de waits fixes. [cite: 1446]
  user: |
    ### [cite_start]Tâche {{TASK_ID}} – Couverture de tests [cite: 1448]
    [cite_start]{{DESCRIPTION}} [cite: 1449]

    [cite_start]*Fichiers* : {{FILES}} [cite: 1450]

    #### [cite_start]Parcours utilisateur à couvrir [cite: 1451]
    1. [cite_start]Authentification Google (mock). [cite: 1452]
    2. [cite_start]Création emprunt → départ → retour. [cite: 1453]
    3. [cite_start]Vérification PDF généré. [cite: 1454]

    #### [cite_start]Critères d’acceptation [cite: 1455]
    - [cite_start]`{{ACCEPTANCE_TEST_CMD}}` vert sur GitHub Actions. [cite: 1456]
    - [cite_start]Temps d’exécution total < 120 s. [cite: 1457]

    [cite_start]Rédige les specs Cypress + fixtures. [cite: 1458]